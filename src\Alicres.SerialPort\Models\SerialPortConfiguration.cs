using System.IO.Ports;
using Alicres.SerialPort.Constants;

namespace Alicres.SerialPort.Models;

/// <summary>
/// 串口配置信息
/// </summary>
public class SerialPortConfiguration
{
    /// <summary>
    /// 端口名称（如 COM1, COM2 等）
    /// </summary>
    public string PortName { get; set; } = string.Empty;

    /// <summary>
    /// 波特率
    /// </summary>
    public int BaudRate { get; set; } = SerialPortConstants.Defaults.BaudRate;

    /// <summary>
    /// 数据位
    /// </summary>
    public int DataBits { get; set; } = SerialPortConstants.Defaults.DataBits;

    /// <summary>
    /// 停止位
    /// </summary>
    public StopBits StopBits { get; set; } = StopBits.One;

    /// <summary>
    /// 校验位
    /// </summary>
    public Parity Parity { get; set; } = Parity.None;

    /// <summary>
    /// 握手协议
    /// </summary>
    public Handshake Handshake { get; set; } = Handshake.None;

    /// <summary>
    /// 读取超时时间（毫秒）
    /// </summary>
    public int ReadTimeout { get; set; } = SerialPortConstants.Defaults.ReadTimeout;

    /// <summary>
    /// 写入超时时间（毫秒）
    /// </summary>
    public int WriteTimeout { get; set; } = SerialPortConstants.Defaults.WriteTimeout;

    /// <summary>
    /// 接收缓冲区大小
    /// </summary>
    public int ReceiveBufferSize { get; set; } = SerialPortConstants.Defaults.ReceiveBufferSize;

    /// <summary>
    /// 发送缓冲区大小
    /// </summary>
    public int SendBufferSize { get; set; } = SerialPortConstants.Defaults.SendBufferSize;

    /// <summary>
    /// 是否启用数据终端就绪信号
    /// </summary>
    public bool DtrEnable { get; set; } = false;

    /// <summary>
    /// 是否启用请求发送信号
    /// </summary>
    public bool RtsEnable { get; set; } = false;

    /// <summary>
    /// 是否启用自动重连
    /// </summary>
    public bool EnableAutoReconnect { get; set; } = false;

    /// <summary>
    /// 重连间隔时间（毫秒）
    /// </summary>
    public int ReconnectInterval { get; set; } = SerialPortConstants.Defaults.ReconnectInterval;

    /// <summary>
    /// 最大重连次数
    /// </summary>
    public int MaxReconnectAttempts { get; set; } = SerialPortConstants.Defaults.MaxReconnectAttempts;

    /// <summary>
    /// 是否启用高级缓冲管理
    /// </summary>
    public bool EnableAdvancedBuffering { get; set; } = false;

    /// <summary>
    /// 内部数据队列最大长度
    /// </summary>
    public int DataQueueMaxLength { get; set; } = SerialPortConstants.Defaults.DataQueueMaxLength;

    /// <summary>
    /// 缓冲区溢出处理策略
    /// </summary>
    public BufferOverflowStrategy BufferOverflowStrategy { get; set; } = BufferOverflowStrategy.DropOldest;

    /// <summary>
    /// 自动清理缓冲区的时间间隔（毫秒）
    /// </summary>
    public int BufferCleanupInterval { get; set; } = SerialPortConstants.Defaults.BufferCleanupInterval;

    /// <summary>
    /// 缓冲区使用率警告阈值（百分比）
    /// </summary>
    public int BufferWarningThreshold { get; set; } = SerialPortConstants.Defaults.BufferWarningThreshold;

    /// <summary>
    /// 验证配置是否有效
    /// </summary>
    /// <returns>如果配置有效返回 true，否则返回 false</returns>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(PortName) &&
               BaudRate > 0 &&
               DataBits >= 5 && DataBits <= 8 &&
               ReadTimeout >= 0 &&
               WriteTimeout >= 0 &&
               ReceiveBufferSize > 0 &&
               SendBufferSize > 0 &&
               ReconnectInterval >= 0 &&
               MaxReconnectAttempts >= 0;
    }

    /// <summary>
    /// 创建默认配置
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>默认配置实例</returns>
    public static SerialPortConfiguration CreateDefault(string portName)
    {
        return new SerialPortConfiguration
        {
            PortName = portName
        };
    }
}
