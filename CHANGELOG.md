# 更新日志

本文档记录了 Alicres.SerialPort 项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [1.0.1] - 2024-06-11

### ✨ 新增功能

**包图标支持**
- ✅ 添加了 NuGet 包图标配置
- ✅ 配置了全局图标管理系统  
- ✅ 统一了所有 Alicres 系列包的图标标准

**构建改进**
- ✅ 优化了全局构建配置
- ✅ 改进了包生成流程
- ✅ 增强了图标包含机制

### 🔧 技术改进

**项目配置**
- ✅ 在 `Directory.Build.props` 中添加了统一图标配置
- ✅ 简化了项目文件中的重复配置
- ✅ 建立了 `src/icon.png` 作为统一图标源

**包管理**
- ✅ 改进了 NuGet 包元数据
- ✅ 优化了包内容组织
- ✅ 增强了包的视觉识别度

### 📦 发布信息

- **版本**: 1.0.1
- **发布时间**: 2024-06-11
- **包大小**: 优化后的包大小
- **新增内容**: 包图标支持

## [1.0.0] - 2024-06-11

### 🎉 首次发布

#### ✨ 新增功能

**核心功能**
- ✅ 串口连接管理（打开、关闭、状态检查）
- ✅ 数据收发功能（同步/异步方式）
- ✅ 串口参数配置（波特率、数据位、停止位、校验位等）
- ✅ 事件驱动的数据接收处理
- ✅ 错误处理和异常管理
- ✅ 连接状态监控和自动重连机制

**接口设计**
- ✅ `ISerialPortService` - 核心串口服务接口
- ✅ `ISerialPortManager` - 串口管理器接口
- ✅ 完整的事件参数定义

**数据模型**
- ✅ `SerialPortConfiguration` - 串口配置模型
- ✅ `SerialPortStatus` - 连接状态模型  
- ✅ `SerialPortData` - 数据传输模型
- ✅ 支持多种数据格式转换（文本、十六进制）

**核心服务**
- ✅ `SerialPortService` - 主要服务实现
- ✅ `SerialPortManager` - 连接管理器
- ✅ 完整的异步操作支持

**异常处理**
- ✅ `SerialPortException` - 基础异常类
- ✅ `SerialPortConnectionException` - 连接异常
- ✅ `SerialPortConfigurationException` - 配置异常
- ✅ `SerialPortDataException` - 数据传输异常

**扩展功能**
- ✅ 依赖注入扩展方法
- ✅ 配置选项支持
- ✅ 多端口管理功能

#### 🏗️ 项目基础设施

**构建配置**
- ✅ `Directory.Build.props` - 统一构建属性
- ✅ `Directory.Packages.props` - 中央包管理
- ✅ `global.json` - .NET SDK 版本控制
- ✅ `.editorconfig` - 代码格式规范

**项目结构**
- ✅ 标准的 Alicres 项目目录结构
- ✅ 接口优先的设计模式
- ✅ 完整的 XML 文档注释

**质量保证**
- ✅ 单元测试覆盖率 ≥ 80%（53个测试用例）
- ✅ 代码分析和警告处理
- ✅ NuGet 包发布配置

#### 📚 文档和示例

**文档**
- ✅ 完整的 README.md 文档
- ✅ API 文档和使用指南
- ✅ 项目架构说明

**示例项目**
- ✅ 基本串口通讯示例
- ✅ 串口管理器使用示例
- ✅ 自动重连功能演示
- ✅ 数据格式转换示例
- ✅ 依赖注入集成示例

#### 🧪 测试覆盖

**模型测试**
- ✅ `SerialPortConfigurationTests` - 配置模型测试
- ✅ `SerialPortDataTests` - 数据模型测试
- ✅ 边界条件和异常情况测试

**测试框架**
- ✅ xUnit 测试框架
- ✅ FluentAssertions 断言库
- ✅ Moq 模拟对象框架

#### 📦 NuGet 包

**包信息**
- ✅ 包 ID: `Alicres.SerialPort`
- ✅ 版本: `1.0.0`
- ✅ 目标框架: `.NET 8.0`
- ✅ 许可证: `MIT`

**包内容**
- ✅ 主程序集和依赖项
- ✅ XML 文档文件
- ✅ 符号包（.snupkg）
- ✅ README 文件

---

## 版本说明

### 版本号规则
本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范：

- **主版本号**：不兼容的 API 修改
- **次版本号**：向下兼容的功能性新增  
- **修订号**：向下兼容的问题修正

### 发布周期
- **主版本**：根据重大功能更新或架构变更发布
- **次版本**：每月发布，包含新功能和改进
- **修订版本**：根据需要发布，主要修复 bug

### 支持政策
- 当前主版本：完全支持
- 前一个主版本：安全更新和关键 bug 修复
- 更早版本：不再维护

---

*更多信息请访问 [GitHub 仓库](https://github.com/alicres/Alicres.SerialPort)*
