# Alicres - C# 功能库集合

[![.NET 8.0](https://img.shields.io/badge/.NET-8.0-blue.svg)](https://dotnet.microsoft.com/download/dotnet/8.0)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Gitee](https://img.shields.io/badge/Gitee-alicres-red.svg)](https://gitee.com/wzhdsg/alicres)

Alicres 是一个高质量的 C# 功能库集合，专为 .NET 开发者提供常用的功能模块。所有库都遵循统一的开发规范，确保代码质量、一致性和可维护性。

## 📦 功能库列表

### 🔌 Alicres.SerialPort - 串口通讯库

[![NuGet Version](https://img.shields.io/nuget/v/Alicres.SerialPort.svg)](https://www.nuget.org/packages/Alicres.SerialPort/)
[![NuGet Downloads](https://img.shields.io/nuget/dt/Alicres.SerialPort.svg)](https://www.nuget.org/packages/Alicres.SerialPort/)

**功能简介**：
- 串口连接管理（打开、关闭、状态检查）
- 异步数据收发功能
- 事件驱动的数据接收处理
- 自动重连机制
- 多端口管理
- 完善的异常处理

**安装方式**：
```bash
dotnet add package Alicres.SerialPort
```

**基本使用**：
```csharp
var config = new SerialPortConfiguration { PortName = "COM1", BaudRate = 9600 };
using var serialPort = new SerialPortService(config, logger);
await serialPort.OpenAsync();
await serialPort.SendTextAsync("Hello, Serial Port!");
```

---

## 🏗️ 项目结构

```
Alicres/
├── src/                          # 源代码目录
│   ├── Alicres.SerialPort/       # 串口通讯库
│   └── icon.png                  # 统一包图标
├── tests/                        # 测试项目
│   └── Alicres.SerialPort.Tests/ # 串口库测试
├── examples/                     # 示例项目
│   └── Alicres.SerialPort.Examples/ # 串口库示例
├── Directory.Build.props         # 全局构建配置
├── Directory.Packages.props      # 中央包管理
└── global.json                   # .NET SDK 版本
```

## 🚀 快速开始

### 环境要求
- .NET 8.0 SDK 或更高版本
- Visual Studio 2022 或 VS Code

### 克隆仓库
```bash
git clone https://gitee.com/wzhdsg/alicres.git
cd alicres
```

### 构建项目
```bash
dotnet restore
dotnet build
```

### 运行测试
```bash
dotnet test
```

### 运行示例
```bash
dotnet run --project examples/Alicres.SerialPort.Examples
```

## 📋 开发规范

所有 Alicres 系列库都遵循以下开发规范：

- **目标框架**：.NET 8.0
- **代码风格**：Microsoft C# 编码约定
- **测试覆盖率**：≥ 80%
- **文档要求**：完整的 XML 注释
- **包管理**：中央包版本管理
- **质量保证**：代码分析 + 单元测试

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 📞 联系方式

- **Gitee**: https://gitee.com/wzhdsg/alicres
- **Issues**: https://gitee.com/wzhdsg/alicres/issues

---

**Alicres** - 让 .NET 开发更简单！
