namespace Alicres.SerialPort.Models;

/// <summary>
/// 缓冲区警告事件参数
/// </summary>
public class BufferWarningEventArgs : EventArgs
{
    /// <summary>
    /// 当前使用率（百分比）
    /// </summary>
    public int UsagePercentage { get; }

    /// <summary>
    /// 当前队列长度
    /// </summary>
    public int CurrentLength { get; }

    /// <summary>
    /// 最大队列长度
    /// </summary>
    public int MaxLength { get; }

    /// <summary>
    /// 警告时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="usagePercentage">使用率</param>
    /// <param name="currentLength">当前长度</param>
    /// <param name="maxLength">最大长度</param>
    public BufferWarningEventArgs(int usagePercentage, int currentLength, int maxLength)
    {
        UsagePercentage = usagePercentage;
        CurrentLength = currentLength;
        MaxLength = maxLength;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] 缓冲区警告: 使用率 {UsagePercentage}% ({CurrentLength}/{MaxLength})";
    }
}

/// <summary>
/// 缓冲区溢出事件参数
/// </summary>
public class BufferOverflowEventArgs : EventArgs
{
    /// <summary>
    /// 当前队列长度
    /// </summary>
    public int CurrentLength { get; }

    /// <summary>
    /// 最大队列长度
    /// </summary>
    public int MaxLength { get; }

    /// <summary>
    /// 导致溢出的数据
    /// </summary>
    public SerialPortData OverflowData { get; }

    /// <summary>
    /// 溢出时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="currentLength">当前长度</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="overflowData">溢出数据</param>
    public BufferOverflowEventArgs(int currentLength, int maxLength, SerialPortData overflowData)
    {
        CurrentLength = currentLength;
        MaxLength = maxLength;
        OverflowData = overflowData ?? throw new ArgumentNullException(nameof(overflowData));
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] 缓冲区溢出: 队列已满 ({CurrentLength}/{MaxLength}), 数据长度: {OverflowData.Length} 字节";
    }
}
