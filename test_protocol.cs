using System;
using System.Threading.Tasks;
using Alicres.Protocol.Protocols.Modbus;
using Alicres.Protocol.Protocols.Modbus.Messages;
using Alicres.Protocol.Validators;

class Program
{
    static async Task Main()
    {
        Console.WriteLine("=== Alicres.Protocol 功能验证 ===\n");

        // 1. 测试 CRC16 校验器
        Console.WriteLine("1. 测试 CRC16 校验器:");
        var validator = new Crc16Validator();
        var testData = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 };
        var dataWithCrc = validator.AddChecksum(testData);
        var isValid = validator.Validate(dataWithCrc);
        Console.WriteLine($"   原始数据: {BitConverter.ToString(testData)}");
        Console.WriteLine($"   带CRC数据: {BitConverter.ToString(dataWithCrc)}");
        Console.WriteLine($"   校验结果: {(isValid ? "通过" : "失败")}");

        // 2. 测试 Modbus RTU 协议
        Console.WriteLine("\n2. 测试 Modbus RTU 协议:");
        var protocol = new ModbusRtuProtocol();
        Console.WriteLine($"   协议名称: {protocol.ProtocolName}");
        Console.WriteLine($"   协议版本: {protocol.ProtocolVersion}");

        // 3. 测试 Modbus 消息创建和序列化
        Console.WriteLine("\n3. 测试 Modbus 消息:");
        var request = new ModbusReadHoldingRegistersRequest(1, 0, 10);
        Console.WriteLine($"   请求消息: {request}");
        
        var serializedRequest = await protocol.SerializeAsync(request);
        Console.WriteLine($"   序列化数据: {BitConverter.ToString(serializedRequest)}");

        // 4. 测试消息解析
        Console.WriteLine("\n4. 测试消息解析:");
        var parsedMessage = await protocol.ParseAsync(serializedRequest);
        if (parsedMessage is ModbusReadHoldingRegistersRequest parsedRequest)
        {
            Console.WriteLine($"   解析成功: 从站地址={parsedRequest.SlaveAddress}, 起始地址={parsedRequest.StartAddress}, 数量={parsedRequest.RegisterCount}");
        }

        // 5. 测试响应消息
        Console.WriteLine("\n5. 测试响应消息:");
        var responseData = new ushort[] { 100, 200, 300, 400, 500 };
        var response = new ModbusReadHoldingRegistersResponse(1, responseData);
        Console.WriteLine($"   响应消息: {response}");
        
        var serializedResponse = await protocol.SerializeAsync(response);
        Console.WriteLine($"   序列化数据: {BitConverter.ToString(serializedResponse)}");

        Console.WriteLine("\n=== 所有测试完成 ===");
    }
}
