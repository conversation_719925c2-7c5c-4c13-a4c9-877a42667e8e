namespace Alicres.SerialPort.Models;

/// <summary>
/// 缓冲区统计信息
/// </summary>
public class BufferStatistics
{
    /// <summary>
    /// 当前队列长度
    /// </summary>
    public int QueueLength { get; set; }

    /// <summary>
    /// 队列使用率（百分比）
    /// </summary>
    public int QueueUsagePercentage { get; set; }

    /// <summary>
    /// 最大队列长度
    /// </summary>
    public int MaxQueueLength { get; set; }

    /// <summary>
    /// 总接收字节数
    /// </summary>
    public long TotalBytesReceived { get; set; }

    /// <summary>
    /// 总丢弃字节数
    /// </summary>
    public long TotalBytesDropped { get; set; }

    /// <summary>
    /// 数据丢失率（百分比）
    /// </summary>
    public double DataLossRate => TotalBytesReceived > 0 ? (double)TotalBytesDropped / TotalBytesReceived * 100 : 0;

    /// <summary>
    /// 最后清理时间
    /// </summary>
    public DateTime LastCleanupTime { get; set; }

    /// <summary>
    /// 溢出处理策略
    /// </summary>
    public BufferOverflowStrategy OverflowStrategy { get; set; }

    /// <summary>
    /// 统计信息生成时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>统计信息的字符串描述</returns>
    public override string ToString()
    {
        return $"缓冲区统计 [{Timestamp:HH:mm:ss.fff}]: " +
               $"队列 {QueueLength}/{MaxQueueLength} ({QueueUsagePercentage}%), " +
               $"接收 {TotalBytesReceived} 字节, " +
               $"丢弃 {TotalBytesDropped} 字节 ({DataLossRate:F2}%), " +
               $"策略: {OverflowStrategy}";
    }

    /// <summary>
    /// 获取详细报告
    /// </summary>
    /// <returns>详细的统计报告</returns>
    public string GetDetailedReport()
    {
        var report = new System.Text.StringBuilder();
        report.AppendLine("=== 缓冲区统计报告 ===");
        report.AppendLine($"生成时间: {Timestamp:yyyy-MM-dd HH:mm:ss.fff}");
        report.AppendLine($"队列状态: {QueueLength}/{MaxQueueLength} ({QueueUsagePercentage}%)");
        report.AppendLine($"数据统计:");
        report.AppendLine($"  - 总接收: {TotalBytesReceived:N0} 字节");
        report.AppendLine($"  - 总丢弃: {TotalBytesDropped:N0} 字节");
        report.AppendLine($"  - 丢失率: {DataLossRate:F2}%");
        report.AppendLine($"配置信息:");
        report.AppendLine($"  - 溢出策略: {OverflowStrategy}");
        report.AppendLine($"  - 最后清理: {LastCleanupTime:yyyy-MM-dd HH:mm:ss.fff}");
        
        // 添加健康状态评估
        var healthStatus = GetHealthStatus();
        report.AppendLine($"健康状态: {healthStatus}");
        
        return report.ToString();
    }

    /// <summary>
    /// 获取缓冲区健康状态
    /// </summary>
    /// <returns>健康状态描述</returns>
    public string GetHealthStatus()
    {
        if (QueueUsagePercentage >= 90)
            return "危险 - 队列使用率过高";
        
        if (QueueUsagePercentage >= 80)
            return "警告 - 队列使用率较高";
        
        if (DataLossRate >= 5)
            return "警告 - 数据丢失率较高";
        
        if (DataLossRate >= 1)
            return "注意 - 存在数据丢失";
        
        return "良好 - 运行正常";
    }

    /// <summary>
    /// 获取性能建议
    /// </summary>
    /// <returns>性能优化建议列表</returns>
    public List<string> GetPerformanceSuggestions()
    {
        var suggestions = new List<string>();

        if (QueueUsagePercentage >= 80)
        {
            suggestions.Add("考虑增加队列最大长度或优化数据处理速度");
        }

        if (DataLossRate >= 1)
        {
            suggestions.Add("数据丢失率较高，建议检查数据处理逻辑");
        }

        if (OverflowStrategy == BufferOverflowStrategy.DropNewest)
        {
            suggestions.Add("当前使用丢弃最新数据策略，可能导致数据不连续");
        }

        if (TotalBytesDropped > 0 && OverflowStrategy == BufferOverflowStrategy.ThrowException)
        {
            suggestions.Add("使用异常策略但仍有数据丢失，建议检查异常处理逻辑");
        }

        if (suggestions.Count == 0)
        {
            suggestions.Add("缓冲区运行状态良好，无需特别优化");
        }

        return suggestions;
    }
}
