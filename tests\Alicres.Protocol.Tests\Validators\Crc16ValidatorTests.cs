using Alicres.Protocol.Validators;
using FluentAssertions;
using Xunit;

namespace Alicres.Protocol.Tests.Validators;

/// <summary>
/// CRC16 校验器测试
/// </summary>
public class Crc16ValidatorTests
{
    private readonly Crc16Validator _validator;

    public Crc16ValidatorTests()
    {
        _validator = new Crc16Validator();
    }

    [Fact]
    public void ValidatorName_ShouldReturnCorrectName()
    {
        // Act
        var name = _validator.ValidatorName;

        // Assert
        name.Should().Be("CRC16-MODBUS");
    }

    [Theory]
    [InlineData(new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 }, 0x84, 0x0A)]
    [InlineData(new byte[] { 0x01, 0x03, 0x02, 0x00, 0x0A }, 0xF8, 0x44)]
    [InlineData(new byte[] { 0x02, 0x03, 0x00, 0x00, 0x00, 0x01 }, 0x84, 0x39)]
    public void CalculateCrc16_WithKnownData_ShouldReturnExpectedCrc(byte[] data, byte expectedLow, byte expectedHigh)
    {
        // Act
        var crc = Crc16Validator.CalculateCrc16(data);

        // Assert
        var expectedCrc = (ushort)((expectedHigh << 8) | expectedLow);
        crc.Should().Be(expectedCrc);
    }

    [Fact]
    public void CalculateChecksum_WithValidData_ShouldReturnCrcBytes()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 };

        // Act
        var checksum = _validator.CalculateChecksum(data);

        // Assert
        checksum.Should().HaveCount(2);
        checksum[0].Should().Be(0x84); // CRC 低字节
        checksum[1].Should().Be(0x0A); // CRC 高字节
    }

    [Fact]
    public void AddChecksum_WithValidData_ShouldAppendCrc()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 };

        // Act
        var result = _validator.AddChecksum(data);

        // Assert
        result.Should().HaveCount(8);
        result.Take(6).Should().BeEquivalentTo(data);
        result[6].Should().Be(0x84); // CRC 低字节
        result[7].Should().Be(0x0A); // CRC 高字节
    }

    [Fact]
    public void RemoveChecksum_WithValidData_ShouldRemoveCrc()
    {
        // Arrange
        var dataWithCrc = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x84, 0x0A };

        // Act
        var result = _validator.RemoveChecksum(dataWithCrc);

        // Assert
        result.Should().HaveCount(6);
        result.Should().BeEquivalentTo(new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 });
    }

    [Fact]
    public void Validate_WithValidCrc_ShouldReturnTrue()
    {
        // Arrange
        var dataWithValidCrc = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x84, 0x0A };

        // Act
        var result = _validator.Validate(dataWithValidCrc);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void Validate_WithInvalidCrc_ShouldReturnFalse()
    {
        // Arrange
        var dataWithInvalidCrc = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00 };

        // Act
        var result = _validator.Validate(dataWithInvalidCrc);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void Validate_WithInsufficientData_ShouldReturnFalse()
    {
        // Arrange
        var insufficientData = new byte[] { 0x01 };

        // Act
        var result = _validator.Validate(insufficientData);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task ValidateAsync_WithValidCrc_ShouldReturnTrue()
    {
        // Arrange
        var dataWithValidCrc = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x84, 0x0A };

        // Act
        var result = await _validator.ValidateAsync(dataWithValidCrc);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void RemoveChecksum_WithInsufficientData_ShouldThrowException()
    {
        // Arrange
        var insufficientData = new byte[] { 0x01 };

        // Act & Assert
        var action = () => _validator.RemoveChecksum(insufficientData);
        action.Should().Throw<ArgumentException>()
            .WithMessage("数据长度不足，无法移除CRC校验*");
    }

    [Fact]
    public void CalculateCrc16_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        var action = () => Crc16Validator.CalculateCrc16(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void CalculateCrc16_WithEmptyData_ShouldReturnInitialValue()
    {
        // Arrange
        var emptyData = Array.Empty<byte>();

        // Act
        var crc = Crc16Validator.CalculateCrc16(emptyData);

        // Assert
        crc.Should().Be(0xFFFF); // 初始值
    }

    [Theory]
    [InlineData(new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x84, 0x0A }, true)]
    [InlineData(new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00 }, false)]
    [InlineData(new byte[] { 0x02, 0x03, 0x02, 0x00, 0x0A, 0xF8, 0x44 }, true)]
    public void VerifyCrc16_WithVariousData_ShouldReturnExpectedResult(byte[] data, bool expected)
    {
        // Act
        var result = Crc16Validator.VerifyCrc16(data);

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData(new byte[] { 0x01, 0x03 }, 0, 2)]
    [InlineData(new byte[] { 0x01, 0x03, 0x00, 0x00 }, 1, 2)]
    public void CalculateCrc16_WithOffsetAndCount_ShouldCalculateCorrectly(byte[] data, int offset, int count)
    {
        // Act
        var crc = Crc16Validator.CalculateCrc16(data, offset, count);

        // Assert
        crc.Should().NotBe(0xFFFF); // 应该不等于初始值
    }

    [Fact]
    public void CalculateCrc16_WithInvalidOffset_ShouldThrowException()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x03 };

        // Act & Assert
        var action = () => Crc16Validator.CalculateCrc16(data, -1, 1);
        action.Should().Throw<ArgumentOutOfRangeException>();
    }

    [Fact]
    public void CalculateCrc16_WithInvalidCount_ShouldThrowException()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x03 };

        // Act & Assert
        var action = () => Crc16Validator.CalculateCrc16(data, 0, 5);
        action.Should().Throw<ArgumentOutOfRangeException>();
    }
}
