<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <PackageId>Alicres.SerialPort</PackageId>
    <Version>1.0.1</Version>
    <Description>A comprehensive serial port communication library for .NET applications</Description>
    <PackageTags>serialport;communication;hardware;alicres</PackageTags>
    <PackageProjectUrl>https://github.com/alicres/Alicres.SerialPort</PackageProjectUrl>
    <PackageReadmeFile>README.md</PackageReadmeFile>

  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Options" />
    <PackageReference Include="System.IO.Ports" />
  </ItemGroup>

  <ItemGroup>
    <None Include="README.md" Pack="true" PackagePath="\" />
  </ItemGroup>

</Project>
