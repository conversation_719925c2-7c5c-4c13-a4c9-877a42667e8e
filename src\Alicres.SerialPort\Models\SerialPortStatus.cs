namespace Alicres.SerialPort.Models;

/// <summary>
/// 串口连接状态枚举
/// </summary>
public enum SerialPortConnectionState
{
    /// <summary>
    /// 已断开连接
    /// </summary>
    Disconnected,

    /// <summary>
    /// 正在连接
    /// </summary>
    Connecting,

    /// <summary>
    /// 已连接
    /// </summary>
    Connected,

    /// <summary>
    /// 正在断开连接
    /// </summary>
    Disconnecting,

    /// <summary>
    /// 连接错误
    /// </summary>
    Error,

    /// <summary>
    /// 正在重连
    /// </summary>
    Reconnecting
}

/// <summary>
/// 串口状态信息
/// </summary>
public class SerialPortStatus
{
    /// <summary>
    /// 端口名称
    /// </summary>
    public string PortName { get; set; } = string.Empty;

    /// <summary>
    /// 连接状态
    /// </summary>
    public SerialPortConnectionState ConnectionState { get; set; } = SerialPortConnectionState.Disconnected;

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected => ConnectionState == SerialPortConnectionState.Connected;

    /// <summary>
    /// 最后连接时间
    /// </summary>
    public DateTime? LastConnectedTime { get; set; }

    /// <summary>
    /// 最后断开时间
    /// </summary>
    public DateTime? LastDisconnectedTime { get; set; }

    /// <summary>
    /// 重连次数
    /// </summary>
    public int ReconnectAttempts { get; set; } = 0;

    /// <summary>
    /// 发送字节数
    /// </summary>
    public long BytesSent { get; set; } = 0;

    /// <summary>
    /// 接收字节数
    /// </summary>
    public long BytesReceived { get; set; } = 0;

    /// <summary>
    /// 最后错误信息
    /// </summary>
    public string? LastError { get; set; }

    /// <summary>
    /// 最后错误时间
    /// </summary>
    public DateTime? LastErrorTime { get; set; }

    /// <summary>
    /// 创建新的状态实例
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <param name="connectionState">连接状态</param>
    /// <returns>状态实例</returns>
    public static SerialPortStatus Create(string portName, SerialPortConnectionState connectionState = SerialPortConnectionState.Disconnected)
    {
        return new SerialPortStatus
        {
            PortName = portName,
            ConnectionState = connectionState
        };
    }

    /// <summary>
    /// 更新连接状态
    /// </summary>
    /// <param name="newState">新状态</param>
    public void UpdateConnectionState(SerialPortConnectionState newState)
    {
        var previousState = ConnectionState;
        ConnectionState = newState;

        switch (newState)
        {
            case SerialPortConnectionState.Connected:
                LastConnectedTime = DateTime.Now;
                break;
            case SerialPortConnectionState.Disconnected:
                if (previousState == SerialPortConnectionState.Connected)
                {
                    LastDisconnectedTime = DateTime.Now;
                }
                break;
        }
    }

    /// <summary>
    /// 记录错误信息
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    public void RecordError(string errorMessage)
    {
        LastError = errorMessage;
        LastErrorTime = DateTime.Now;
        ConnectionState = SerialPortConnectionState.Error;
    }

    /// <summary>
    /// 增加重连次数
    /// </summary>
    public void IncrementReconnectAttempts()
    {
        ReconnectAttempts++;
    }

    /// <summary>
    /// 重置重连次数
    /// </summary>
    public void ResetReconnectAttempts()
    {
        ReconnectAttempts = 0;
    }

    /// <summary>
    /// 更新数据统计
    /// </summary>
    /// <param name="sentBytes">发送字节数</param>
    /// <param name="receivedBytes">接收字节数</param>
    public void UpdateDataStatistics(long sentBytes = 0, long receivedBytes = 0)
    {
        BytesSent += sentBytes;
        BytesReceived += receivedBytes;
    }
}
