using Alicres.Protocol.Interfaces;

namespace Alicres.Protocol.Models.EventArgs;

/// <summary>
/// 协议消息事件参数
/// </summary>
public class ProtocolMessageEventArgs : System.EventArgs
{
    /// <summary>
    /// 协议消息
    /// </summary>
    public IProtocolMessage Message { get; }

    /// <summary>
    /// 协议名称
    /// </summary>
    public string ProtocolName { get; }

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">协议消息</param>
    /// <param name="protocolName">协议名称</param>
    public ProtocolMessageEventArgs(IProtocolMessage message, string protocolName)
    {
        Message = message ?? throw new ArgumentNullException(nameof(message));
        ProtocolName = protocolName ?? throw new ArgumentNullException(nameof(protocolName));
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 协议序列化事件参数
/// </summary>
public class ProtocolSerializationEventArgs : System.EventArgs
{
    /// <summary>
    /// 原始消息
    /// </summary>
    public IProtocolMessage Message { get; }

    /// <summary>
    /// 序列化后的数据
    /// </summary>
    public byte[] SerializedData { get; }

    /// <summary>
    /// 协议名称
    /// </summary>
    public string ProtocolName { get; }

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">原始消息</param>
    /// <param name="serializedData">序列化后的数据</param>
    /// <param name="protocolName">协议名称</param>
    public ProtocolSerializationEventArgs(IProtocolMessage message, byte[] serializedData, string protocolName)
    {
        Message = message ?? throw new ArgumentNullException(nameof(message));
        SerializedData = serializedData ?? throw new ArgumentNullException(nameof(serializedData));
        ProtocolName = protocolName ?? throw new ArgumentNullException(nameof(protocolName));
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 协议错误事件参数
/// </summary>
public class ProtocolErrorEventArgs : System.EventArgs
{
    /// <summary>
    /// 错误异常
    /// </summary>
    public Exception Exception { get; }

    /// <summary>
    /// 协议名称
    /// </summary>
    public string ProtocolName { get; }

    /// <summary>
    /// 错误相关的原始数据
    /// </summary>
    public byte[]? RawData { get; }

    /// <summary>
    /// 错误描述
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="exception">错误异常</param>
    /// <param name="protocolName">协议名称</param>
    /// <param name="rawData">错误相关的原始数据</param>
    public ProtocolErrorEventArgs(Exception exception, string protocolName, byte[]? rawData = null)
    {
        Exception = exception ?? throw new ArgumentNullException(nameof(exception));
        ProtocolName = protocolName ?? throw new ArgumentNullException(nameof(protocolName));
        RawData = rawData;
        ErrorMessage = exception.Message;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="errorMessage">错误描述</param>
    /// <param name="protocolName">协议名称</param>
    /// <param name="rawData">错误相关的原始数据</param>
    public ProtocolErrorEventArgs(string errorMessage, string protocolName, byte[]? rawData = null)
    {
        Exception = new InvalidOperationException(errorMessage);
        ProtocolName = protocolName ?? throw new ArgumentNullException(nameof(protocolName));
        RawData = rawData;
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 帧提取事件参数
/// </summary>
public class FrameExtractedEventArgs : System.EventArgs
{
    /// <summary>
    /// 提取出的帧数据
    /// </summary>
    public byte[] FrameData { get; }

    /// <summary>
    /// 帧序号
    /// </summary>
    public int FrameNumber { get; }

    /// <summary>
    /// 帧处理器名称
    /// </summary>
    public string FramingName { get; }

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="frameData">帧数据</param>
    /// <param name="frameNumber">帧序号</param>
    /// <param name="framingName">帧处理器名称</param>
    public FrameExtractedEventArgs(byte[] frameData, int frameNumber, string framingName)
    {
        FrameData = frameData ?? throw new ArgumentNullException(nameof(frameData));
        FrameNumber = frameNumber;
        FramingName = framingName ?? throw new ArgumentNullException(nameof(framingName));
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 帧处理错误事件参数
/// </summary>
public class FramingErrorEventArgs : System.EventArgs
{
    /// <summary>
    /// 错误异常
    /// </summary>
    public Exception Exception { get; }

    /// <summary>
    /// 帧处理器名称
    /// </summary>
    public string FramingName { get; }

    /// <summary>
    /// 错误相关的原始数据
    /// </summary>
    public byte[]? RawData { get; }

    /// <summary>
    /// 错误描述
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="exception">错误异常</param>
    /// <param name="framingName">帧处理器名称</param>
    /// <param name="rawData">错误相关的原始数据</param>
    public FramingErrorEventArgs(Exception exception, string framingName, byte[]? rawData = null)
    {
        Exception = exception ?? throw new ArgumentNullException(nameof(exception));
        FramingName = framingName ?? throw new ArgumentNullException(nameof(framingName));
        RawData = rawData;
        ErrorMessage = exception.Message;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="errorMessage">错误描述</param>
    /// <param name="framingName">帧处理器名称</param>
    /// <param name="rawData">错误相关的原始数据</param>
    public FramingErrorEventArgs(string errorMessage, string framingName, byte[]? rawData = null)
    {
        Exception = new InvalidOperationException(errorMessage);
        FramingName = framingName ?? throw new ArgumentNullException(nameof(framingName));
        RawData = rawData;
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        Timestamp = DateTime.Now;
    }
}
