using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Alicres.SerialPort.Tests.Services;

/// <summary>
/// 高级缓冲管理器测试
/// </summary>
public class AdvancedBufferManagerTests : IDisposable
{
    private readonly Mock<ILogger> _mockLogger;
    private readonly SerialPortConfiguration _configuration;
    private readonly AdvancedBufferManager _bufferManager;

    public AdvancedBufferManagerTests()
    {
        _mockLogger = new Mock<ILogger>();
        _configuration = new SerialPortConfiguration
        {
            PortName = "COM1",
            DataQueueMaxLength = 10,
            BufferOverflowStrategy = BufferOverflowStrategy.DropOldest,
            BufferCleanupInterval = 1000,
            BufferWarningThreshold = 80
        };
        _bufferManager = new AdvancedBufferManager(_configuration, _mockLogger.Object);
    }

    [Fact]
    public void Constructor_WithValidConfiguration_ShouldInitializeCorrectly()
    {
        // Assert
        _bufferManager.QueueLength.Should().Be(0);
        _bufferManager.QueueUsagePercentage.Should().Be(0);
        _bufferManager.TotalBytesReceived.Should().Be(0);
        _bufferManager.TotalBytesDropped.Should().Be(0);
    }

    [Fact]
    public void Constructor_WithNullConfiguration_ShouldThrowException()
    {
        // Act & Assert
        var action = () => new AdvancedBufferManager(null!, _mockLogger.Object);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void EnqueueData_WithValidData_ShouldAddToQueue()
    {
        // Arrange
        var data = new SerialPortData(new byte[] { 1, 2, 3 }, "COM1", SerialPortDataDirection.Received);

        // Act
        var result = _bufferManager.EnqueueData(data);

        // Assert
        result.Should().BeTrue();
        _bufferManager.QueueLength.Should().Be(1);
        _bufferManager.TotalBytesReceived.Should().Be(3);
    }

    [Fact]
    public void EnqueueData_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        var action = () => _bufferManager.EnqueueData(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void EnqueueData_WhenQueueFull_ShouldTriggerOverflowEvent()
    {
        // Arrange
        var overflowEventTriggered = false;
        _bufferManager.BufferOverflow += (sender, e) => overflowEventTriggered = true;

        // Fill the queue to capacity
        for (int i = 0; i < _configuration.DataQueueMaxLength; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Act - Add one more item to trigger overflow
        var overflowData = new SerialPortData(new byte[] { 99 }, "COM1", SerialPortDataDirection.Received);
        var result = _bufferManager.EnqueueData(overflowData);

        // Assert
        overflowEventTriggered.Should().BeTrue();
        result.Should().BeTrue(); // Should succeed with DropOldest strategy
        _bufferManager.QueueLength.Should().Be(_configuration.DataQueueMaxLength);
    }

    [Fact]
    public void EnqueueData_WhenNearCapacity_ShouldTriggerWarningEvent()
    {
        // Arrange
        var warningEventTriggered = false;
        _bufferManager.BufferWarning += (sender, e) => warningEventTriggered = true;

        // Fill queue to warning threshold (80% of 10 = 8 items)
        for (int i = 0; i < 8; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Act - Add one more to trigger warning
        var warningData = new SerialPortData(new byte[] { 99 }, "COM1", SerialPortDataDirection.Received);
        _bufferManager.EnqueueData(warningData);

        // Assert
        warningEventTriggered.Should().BeTrue();
        _bufferManager.QueueUsagePercentage.Should().BeGreaterOrEqualTo(80);
    }

    [Fact]
    public void TryDequeueData_WithDataInQueue_ShouldReturnData()
    {
        // Arrange
        var originalData = new SerialPortData(new byte[] { 1, 2, 3 }, "COM1", SerialPortDataDirection.Received);
        _bufferManager.EnqueueData(originalData);

        // Act
        var result = _bufferManager.TryDequeueData(out var dequeuedData);

        // Assert
        result.Should().BeTrue();
        dequeuedData.Should().NotBeNull();
        dequeuedData!.RawData.Should().BeEquivalentTo(originalData.RawData);
        _bufferManager.QueueLength.Should().Be(0);
    }

    [Fact]
    public void TryDequeueData_WithEmptyQueue_ShouldReturnFalse()
    {
        // Act
        var result = _bufferManager.TryDequeueData(out var data);

        // Assert
        result.Should().BeFalse();
        data.Should().BeNull();
    }

    [Fact]
    public void DequeueBatch_WithMultipleItems_ShouldReturnCorrectCount()
    {
        // Arrange
        for (int i = 0; i < 5; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Act
        var result = _bufferManager.DequeueBatch(3);

        // Assert
        result.Should().HaveCount(3);
        _bufferManager.QueueLength.Should().Be(2);
    }

    [Fact]
    public void DequeueBatch_WithMoreRequestedThanAvailable_ShouldReturnAllAvailable()
    {
        // Arrange
        for (int i = 0; i < 3; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Act
        var result = _bufferManager.DequeueBatch(10);

        // Assert
        result.Should().HaveCount(3);
        _bufferManager.QueueLength.Should().Be(0);
    }

    [Fact]
    public void ClearQueue_WithDataInQueue_ShouldEmptyQueue()
    {
        // Arrange
        for (int i = 0; i < 5; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Act
        _bufferManager.ClearQueue();

        // Assert
        _bufferManager.QueueLength.Should().Be(0);
    }

    [Fact]
    public void GetStatistics_ShouldReturnCorrectInformation()
    {
        // Arrange
        var data = new SerialPortData(new byte[] { 1, 2, 3 }, "COM1", SerialPortDataDirection.Received);
        _bufferManager.EnqueueData(data);

        // Act
        var statistics = _bufferManager.GetStatistics();

        // Assert
        statistics.Should().NotBeNull();
        statistics.QueueLength.Should().Be(1);
        statistics.MaxQueueLength.Should().Be(_configuration.DataQueueMaxLength);
        statistics.TotalBytesReceived.Should().Be(3);
        statistics.OverflowStrategy.Should().Be(_configuration.BufferOverflowStrategy);
    }

    [Theory]
    [InlineData(BufferOverflowStrategy.DropNewest)]
    [InlineData(BufferOverflowStrategy.ThrowException)]
    public void EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(BufferOverflowStrategy strategy)
    {
        // Arrange
        _configuration.BufferOverflowStrategy = strategy;
        var bufferManager = new AdvancedBufferManager(_configuration, _mockLogger.Object);

        // Fill the queue to capacity
        for (int i = 0; i < _configuration.DataQueueMaxLength; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            bufferManager.EnqueueData(data);
        }

        // Act & Assert
        var overflowData = new SerialPortData(new byte[] { 99 }, "COM1", SerialPortDataDirection.Received);

        if (strategy == BufferOverflowStrategy.DropNewest)
        {
            var result = bufferManager.EnqueueData(overflowData);
            result.Should().BeFalse();
            bufferManager.TotalBytesDropped.Should().Be(1);
        }
        else if (strategy == BufferOverflowStrategy.ThrowException)
        {
            var action = () => bufferManager.EnqueueData(overflowData);
            action.Should().Throw<InvalidOperationException>();
        }

        bufferManager.Dispose();
    }

    [Fact]
    public void QueueUsagePercentage_ShouldCalculateCorrectly()
    {
        // Arrange & Act
        for (int i = 0; i < 5; i++)
        {
            var data = new SerialPortData(new byte[] { (byte)i }, "COM1", SerialPortDataDirection.Received);
            _bufferManager.EnqueueData(data);
        }

        // Assert
        var expectedPercentage = (int)((double)5 / _configuration.DataQueueMaxLength * 100);
        _bufferManager.QueueUsagePercentage.Should().Be(expectedPercentage);
    }

    public void Dispose()
    {
        _bufferManager?.Dispose();
    }
}
